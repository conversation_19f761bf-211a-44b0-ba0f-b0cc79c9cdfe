# 🚀 ML Model Training & Deployment Guide

## 📋 Overview

This guide explains how to properly train the ML models separately from the UI and then deploy the dashboard to display the results.

## 🔄 Workflow

### 1. **Train Models (Offline)**
```bash
# Run the training script
python train_ml_models.py
```

### 2. **Launch Dashboard (Display Only)**
```bash
# Launch the dashboard to display results
streamlit run comprehensive_dashboard.py
```

## 🎯 Training Process

### **Step 1: Model Training**

The training script (`train_ml_models.py`) performs the following:

1. **Data Loading**: Loads 50K+ Twitter users and 47K+ wallet transactions
2. **Feature Engineering**: Creates 14 comprehensive features
3. **Model Training**: Trains multiple models with different sample sizes
4. **Model Evaluation**: Tests accuracy and performance
5. **Model Persistence**: Saves trained models to `trained_models.pkl`

**Training Results:**
```
🎯 Training with 5,000 samples:
📈 Random Forest R²: 0.9493 (94.93% accuracy)
📈 XGBoost R²: 0.9709 (97.09% accuracy)
⏱️ Training time: 2.06 seconds
🔧 Features used: 14
```

### **Step 2: Model Persistence**

Trained models are automatically saved to:
- **File**: `trained_models.pkl`
- **Contents**: All trained models, scalers, and configurations
- **Size**: Optimized for fast loading

### **Step 3: Dashboard Deployment**

The dashboard (`comprehensive_dashboard.py`) automatically:

1. **Loads Pre-trained Models**: Reads from `trained_models.pkl`
2. **Displays Model Status**: Shows training status in sidebar
3. **Provides Predictions**: Uses trained models for real-time scoring
4. **Shows Explanations**: SHAP explainability with trained models

## 📊 Model Architecture

### **Trained Models:**

1. **Random Forest Regressor**
   - Purpose: Primary credit score prediction
   - Accuracy: 94.93% R²
   - Features: 14 comprehensive features

2. **XGBoost Regressor**
   - Purpose: Enhanced prediction accuracy
   - Accuracy: 97.09% R²
   - Features: Same 14 features

3. **Isolation Forest**
   - Purpose: Anomaly detection
   - Function: Identifies unusual behavior patterns

4. **DBSCAN Clustering**
   - Purpose: User segmentation
   - Function: Groups similar users

5. **K-Means Clustering**
   - Purpose: Risk group assignment
   - Function: 5-cluster user categorization

6. **SHAP Explainer**
   - Purpose: Model transparency
   - Function: Feature attribution and explanations

### **Feature Set (14 Features):**

**Financial Features (9):**
- `total_borrowed`
- `total_repaid`
- `total_deposited`
- `num_borrows`
- `num_repays`
- `unique_protocols`
- `unique_tokens`
- `total_transactions`
- `days_active`

**Social Features (5):**
- `social_score`
- `engagement_score`
- `influence_score`
- `quality_score`
- `activity_score`

## 🎛️ Dashboard Features

### **Model Status Display:**
- ✅ **Trained**: Shows green status with model details
- ❌ **Not Trained**: Shows red status with training instructions

### **Real-time Predictions:**
- **ML Predictions**: Uses ensemble of RF + XGBoost
- **Anomaly Detection**: Identifies suspicious patterns
- **Clustering**: Assigns users to risk groups
- **SHAP Explanations**: Shows feature contributions

### **Performance Metrics:**
- **Accuracy**: 97.09% (XGBoost)
- **Speed**: <2 seconds per prediction
- **Scalability**: 50K+ users processed

## 🔧 Commands Reference

### **Training Commands:**
```bash
# Full training with all sample sizes
python train_ml_models.py

# Check if models exist
ls -la trained_models.pkl

# View training logs
python train_ml_models.py > training_log.txt
```

### **Dashboard Commands:**
```bash
# Launch dashboard (auto-loads models)
streamlit run comprehensive_dashboard.py

# Launch on specific port
streamlit run comprehensive_dashboard.py --server.port 8508

# Check dashboard status
curl http://localhost:8501
```

### **Model Management:**
```bash
# Remove models (force retrain)
rm trained_models.pkl

# Backup models
cp trained_models.pkl trained_models_backup.pkl

# Check model file size
du -h trained_models.pkl
```

## 📈 Performance Optimization

### **Training Optimization:**
- **Sample Sizes**: 1K, 3K, 5K for progressive training
- **Feature Selection**: 14 most important features
- **Model Selection**: Best performing algorithms
- **Caching**: Efficient data loading and processing

### **Dashboard Optimization:**
- **Model Loading**: One-time load at startup
- **Caching**: Streamlit caching for data and models
- **Lazy Loading**: Load models only when needed
- **Memory Management**: Efficient model storage

## 🚨 Troubleshooting

### **Common Issues:**

1. **Models Not Found**
   ```
   ❌ Model file trained_models.pkl not found!
   ```
   **Solution**: Run `python train_ml_models.py` first

2. **Training Fails**
   ```
   ❌ Training failed: [error]
   ```
   **Solution**: Check data files and dependencies

3. **Dashboard Shows "Not Trained"**
   ```
   ❌ ML models not trained!
   ```
   **Solution**: Ensure `trained_models.pkl` exists

4. **Prediction Errors**
   ```
   ❌ Prediction failed
   ```
   **Solution**: Restart dashboard to reload models

## ✅ Verification Steps

### **After Training:**
1. Check file exists: `ls trained_models.pkl`
2. Check file size: `du -h trained_models.pkl`
3. Verify training logs for accuracy metrics

### **After Dashboard Launch:**
1. Check sidebar shows "✅ ML models are trained!"
2. Test predictions on sample users
3. Verify all 6 dashboard pages work
4. Check SHAP explanations display

## 🎯 Best Practices

1. **Always train models before launching dashboard**
2. **Keep `trained_models.pkl` in the same directory**
3. **Monitor model performance and retrain periodically**
4. **Use version control for model files**
5. **Test predictions after each training session**

---

**🚀 This separation ensures clean architecture: Train offline, display online!**
